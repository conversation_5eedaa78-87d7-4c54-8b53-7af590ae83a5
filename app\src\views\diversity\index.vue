<template>
  <div class="submit-page">
    <div class="container-fluid">
      <el-row :gutter="15" class="mt-1">
        <el-col :span="6">
          <div class="card">
            <el-tabs
              v-model="form.analysisType"
              @tab-change="handleAnalysisTypeChange"
            >
              <el-tab-pane label="Biogeography" name="Biogeography">
                <el-form
                  :model="form"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <!-- Species Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Species
                      </div>
                    </template>
                    <div class="species-section">
                      <!-- Domain Radio Button Group -->
                      <el-form-item label="Domain" class="mb-2">
                        <el-radio-group
                          v-model="form.domain"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="A">Archaea</el-radio-button>
                          <el-radio-button value="B">Bacteria</el-radio-button>
                          <el-radio-button value="E"
                            >Eukaryota
                          </el-radio-button>
                          <el-radio-button value="V">Virus</el-radio-button>
                        </el-radio-group>
                      </el-form-item>

                      <!-- Taxonomy Radio Button Group -->
                      <el-form-item label="Taxonomy" class="mb-2">
                        <el-radio-group
                          v-model="form.taxonomy"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="P">Phylum</el-radio-button>
                          <el-radio-button value="C">Class</el-radio-button>
                          <el-radio-button value="O">Order</el-radio-button>
                          <el-radio-button value="F">Family</el-radio-button>
                          <el-radio-button value="G">Genus</el-radio-button>
                          <el-radio-button value="S">Species</el-radio-button>
                        </el-radio-group>
                      </el-form-item>

                      <!-- Species Name Multi-select -->
                      <el-form-item label="Species Name" class="mb-2">
                        <el-select
                          v-model="form.speciesNames"
                          multiple
                          filterable
                          :teleported="false"
                          placeholder="Select species names"
                          class="w-100"
                          :max-collapse-tags="3"
                        >
                          <el-option
                            v-for="it in speciesNameOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          />
                        </el-select>
                        <div class="species-warning">
                          The maximum number of queried species name is 10
                        </div>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <!-- Data Filter Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Data Select
                      </div>
                    </template>
                    <!-- Biogeography Mode -->
                    <div class="data-filter-section">
                      <el-form-item label="Longitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLongitude"
                          range
                          :max="180.0"
                          :min="-180.0"
                          :step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.longitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.longitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item label="Latitude" class="mb-2">
                        <el-slider
                          v-model="form.sliderLatitude"
                          range
                          :max="90"
                          :min="-90"
                          :step="0.01"
                          placement="right"
                        />
                        <div class="d-flex align-items-center justify-center">
                          <el-input
                            v-model="form.latitudeTo"
                            style="width: 130px"
                          />
                          <span class="mr-05 ml-05">~</span>
                          <el-input
                            v-model="form.latitudeFrom"
                            style="width: 130px"
                          />
                        </div>
                      </el-form-item>

                      <el-form-item
                        label="Water Body Type"
                        prop="waterBodyType"
                        class="mb-2"
                      >
                        <el-cascader
                          v-model="form.waterBodyType"
                          :popper-append-to-body="false"
                          :options="waterBodyOpt"
                          :props="props"
                          class="w-100"
                          placeholder="Select"
                          @change="handleWaterBodyTypeChange"
                        />
                      </el-form-item>

                      <el-form-item
                        class="mb-2"
                        label="Or Select Data from Cart"
                        prop="selectedGroup"
                      >
                        <el-select
                          v-model="form.selectedGroup"
                          :teleported="false"
                          placeholder="Select a group"
                          clearable
                        >
                          <el-option
                            v-for="it in groupOptions"
                            :key="it.value"
                            :label="it.label"
                            :value="it.value"
                          />
                        </el-select>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      :loading="submitLoading"
                      @click="submitBiogeography"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
              <el-tab-pane label="Species Diversity" name="Species Diversity">
                <el-form
                  :model="form"
                  label-width="auto"
                  style="max-width: 600px"
                  label-position="top"
                >
                  <!-- Species Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Species
                      </div>
                    </template>
                    <div class="species-section">
                      <!-- Domain Radio Button Group -->
                      <el-form-item label="Domain" class="mb-2">
                        <el-radio-group
                          v-model="form.domain"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="archaea"
                            >Archaea
                          </el-radio-button>
                          <el-radio-button value="bacteria"
                            >Bacteria
                          </el-radio-button>
                          <el-radio-button value="eukaryota"
                            >Eukaryota
                          </el-radio-button>
                          <el-radio-button value="virus">Virus</el-radio-button>
                        </el-radio-group>
                      </el-form-item>

                      <!-- Taxonomy Radio Button Group -->
                      <el-form-item label="Taxonomy" class="mb-2">
                        <el-radio-group
                          v-model="form.taxonomy"
                          class="custom-radio-buttons"
                        >
                          <el-radio-button value="phylum"
                            >Phylum
                          </el-radio-button>
                          <el-radio-button value="class">Class</el-radio-button>
                          <el-radio-button value="order">Order</el-radio-button>
                          <el-radio-button value="family"
                            >Family
                          </el-radio-button>
                          <el-radio-button value="genus">Genus</el-radio-button>
                          <el-radio-button value="species"
                            >Species
                          </el-radio-button>
                        </el-radio-group>
                      </el-form-item>
                    </div>
                  </el-form-item>

                  <!-- Data Select Section -->
                  <el-form-item>
                    <template #label>
                      <div class="section-label">
                        <el-icon color="#0080B0" size="14">
                          <Menu />
                        </el-icon>
                        Data Select
                      </div>
                    </template>

                    <!-- Species Diversity Mode -->
                    <div class="data-filter-section">
                      <div class="mb-1">
                        <el-radio-group v-model="form.type">
                          <el-radio value="From Biota">From Biota</el-radio>
                          <el-radio value="From Cart">From Cart</el-radio>
                        </el-radio-group>
                      </div>
                      <div
                        v-for="(group, index) in form.diversityGroups"
                        :key="group.id"
                        class="diversity-group mb-3"
                      >
                        <div
                          class="group-header d-flex justify-space-between align-items-center mb-2"
                        >
                          <el-input
                            v-model="group.name"
                            class="group-title-input"
                            size="small"
                            :style="{ width: '120px' }"
                          />
                          <div class="group-actions">
                            <el-button
                              v-if="index === form.diversityGroups.length - 1"
                              type="success"
                              size="small"
                              round
                              :icon="Plus"
                              @click="addGroup"
                            >
                            </el-button>
                            <el-button
                              v-if="form.diversityGroups.length > 1"
                              type="danger"
                              size="small"
                              round
                              :icon="Minus"
                              @click="removeGroup(index)"
                            >
                            </el-button>
                          </div>
                        </div>

                        <div class="group-content">
                          <el-form-item
                            v-if="form.type === 'From Biota'"
                            class="mb-2"
                            label="Water Body Type"
                          >
                            <el-cascader
                              v-model="group.hydrosphereType"
                              :popper-append-to-body="false"
                              :options="hydrosphereOpt"
                              :props="props"
                              class="w-100"
                              placeholder="Select"
                            />
                          </el-form-item>

                          <el-form-item
                            v-else
                            class="mb-2"
                            label="Select Group from Cart"
                          >
                            <el-select
                              v-model="group.selectedGroups"
                              :teleported="false"
                              placeholder="Select"
                            >
                              <el-option
                                v-for="it in groupOptions"
                                :key="it.value"
                                :label="it.label"
                                :value="it.value"
                              />
                            </el-select>
                          </el-form-item>
                        </div>
                      </div>
                    </div>
                  </el-form-item>

                  <el-form-item>
                    <el-button
                      type="primary"
                      :icon="Promotion"
                      class="w-100 filter-search mt-1"
                      >Submit
                    </el-button>
                  </el-form-item>
                </el-form>
              </el-tab-pane>
            </el-tabs>
          </div>
        </el-col>
        <el-col :span="18">
          <!-- Biogeography 结果显示区域 -->
          <div
            v-show="form.analysisType === 'Biogeography'"
            class="card mb-1 pos-relative"
          >
            <!-- 有结果时显示地图和数据 -->
            <div v-show="hasResults">
              <div class="d-flex justify-space-between align-items-center">
                <h3 class="mb-0 mt-0">
                  <span class="mr-05 font-600"
                    >Global distribution and relativate abundance of</span
                  >

                  <el-select
                    v-model="summarySelect"
                    :teleported="false"
                    filterable
                    placeholder="Select species names"
                    class="mr-1"
                    style="width: 736px"
                  >
                    <el-option
                      v-for="species in speciesNameList"
                      :key="species"
                      :label="species"
                      :value="species"
                    />
                  </el-select>
                </h3>
              </div>
              <el-divider class="mt-1"></el-divider>

              <div
                class="map-container"
                :class="{ fullscreen: isMapFullscreen }"
              >
                <div
                  id="diversityMap"
                  style="width: 100%; height: 560px; background-color: #fffff5"
                ></div>
                <!-- 全屏切换按钮 -->
                <el-button
                  class="fullscreen-btn"
                  type="primary"
                  :icon="isMapFullscreen ? 'FullScreen' : 'Rank'"
                  circle
                  :title="isMapFullscreen ? '退出全屏' : '全屏显示'"
                  @click="toggleMapFullscreen"
                >
                </el-button>
              </div>
              <div class="chart-card">
                <!-- 检测/选择样本信息 -->
                <div class="sample-info">
                  <span>Detected/Selected Samples:</span>
                  <span class="sample-count"
                    >{{ sampleStatistics.detectedSamples }}/{{
                      sampleStatistics.selectedSamples
                    }}</span
                  >
                </div>

                <!-- 标准化丰度统计 -->
                <div class="sample-info">
                  <div>Normalized Abundance (Min/Median/MAX):</div>
                  <div class="sample-count">
                    {{ sampleStatistics.min }}/{{ sampleStatistics.median }}/{{
                      sampleStatistics.max
                    }}
                  </div>
                </div>

                <!-- 图例圆圈 -->
                <div class="legend-container">
                  <div class="legend-item">
                    <div class="size-label">0.0001%</div>
                    <div class="circle" style="width: 7px; height: 7px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.001%</div>
                    <div class="circle" style="width: 11px; height: 11px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.01%</div>
                    <div class="circle" style="width: 17px; height: 17px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">0.1%</div>
                    <div class="circle" style="width: 21px; height: 21px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">1%</div>
                    <div class="circle" style="width: 25px; height: 25px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">10%</div>
                    <div class="circle" style="width: 30px; height: 30px"></div>
                  </div>
                  <div class="legend-item">
                    <div class="size-label">100%</div>
                    <div class="circle" style="width: 35px; height: 35px"></div>
                  </div>
                </div>
              </div>
            </div>
            <!-- 无结果时显示空状态 -->
            <div v-show="!hasResults">
              <el-empty
                description="Please select parameters from the left panel and submit to view results"
                :image-size="200"
              >
                <template #image>
                  <el-icon size="100" color="#c0c4cc">
                    <DataAnalysis />
                  </el-icon>
                </template>
              </el-empty>
            </div>
          </div>
          <div
            v-show="form.analysisType === 'Species Diversity'"
            class="card mb-1 pos-relative"
          >
            <div class="chart-container">
              <div v-if="form.type === 'From Biota'" class="d-flex gap-30 mb-3">
                <img
                  src="@/assets/images/img_5.png"
                  alt="Chart 1"
                  style="width: 48%"
                />
                <img
                  src="@/assets/images/img_5.png"
                  alt="Chart 1"
                  style="width: 48%"
                />
              </div>
              <!-- 轮播图替换原来的两张图片 -->
              <div v-else class="chart-swiper-container mb-3">
                <swiper
                  :modules="swiperModules"
                  :slides-per-view="1"
                  :space-between="20"
                  :navigation="true"
                  :pagination="{ clickable: true }"
                  :loop="true"
                  class="chart-swiper"
                  @swiper="onChartSwiper"
                >
                  <swiper-slide>
                    <div class="swiper-slide-content">
                      <img
                        src="@/assets/images/img_5.png"
                        alt="Chart 1"
                        class="chart-image"
                      />
                    </div>
                  </swiper-slide>
                  <swiper-slide>
                    <div class="swiper-slide-content">
                      <img
                        src="@/assets/images/img_5.png"
                        alt="Chart 2"
                        class="chart-image"
                      />
                    </div>
                  </swiper-slide>
                </swiper>
              </div>
              <div class="text-center font-18 font-600">
                Taxonomic Composition of Selected Samples
                <el-icon color="#1F77B4" class="ml-05 cursor-pointer">
                  <Download />
                </el-icon>
              </div>
              <div
                id="chart01"
                ref="barRef"
                style="width: 100%; height: 450px"
              ></div>
              <div class="mt-2 d-flex">
                <div class="w-50">
                  <div class="text-center font-18 font-600">
                    A barplot of the LDA values distribution, LDA >
                    2,P-value<0.05
                    <el-icon color="#1F77B4" class="ml-05 cursor-pointer">
                      <Download />
                    </el-icon>
                  </div>
                  <div
                    id="ldaChart"
                    ref="ldaRef"
                    style="width: 100%; height: 500px"
                  ></div>
                </div>

                <div class="w-50">
                  <div class="text-center font-18 font-600">
                    Taxonomic Composition of Selected Samples
                    <el-icon color="#1F77B4" class="ml-05 cursor-pointer">
                      <Download />
                    </el-icon>
                  </div>
                  <img
                    src="@/assets/images/diver-distances.png"
                    style="width: 100%"
                    alt=""
                  />
                </div>
              </div>
            </div>
          </div>
          <div class="card">
            <!-- 有结果时显示表格 -->
            <div v-show="hasResults">
              <div class="d-flex justify-space-between align-items-center">
                <h3 class="mb-0 mt-0">Sample List</h3>
                <el-popover
                  placement="bottom-end"
                  :width="300"
                  trigger="hover"
                  popper-class="metadata-popover"
                >
                  <template #reference>
                    <el-button type="success"> Add other metadata</el-button>
                  </template>
                  <div class="metadata-selector">
                    <h4 class="metadata-title">Select Columns to Display</h4>
                    <div class="column-checkboxes">
                      <el-checkbox
                        v-for="column in allColumns"
                        :key="column.prop"
                        v-model="column.visible"
                        :label="column.label"
                        class="column-checkbox"
                      />
                    </div>
                  </div>
                </el-popover>
              </div>
              <el-divider class="mt-1"></el-divider>
              <el-table
                ref="table"
                tooltip-effect="dark"
                :data="dataTable"
                :header-cell-style="{
                  backgroundColor: '#F1F5F9',
                  color: '#333333',
                  fontWeight: 700,
                }"
                border
                :stripe="true"
              >
                <!-- Default visible columns -->
                <el-table-column
                  v-if="getColumnVisibility('runId')"
                  label="Run ID"
                  prop="runId"
                  width="200"
                >
                  <template #default="scope">
                    <div class="d-flex">
                      <el-tag
                        v-if="scope.row.group"
                        effect="dark"
                        round
                        :type="getGroupTagType(scope.row.group)"
                        size="small"
                        class="mr-05"
                      >
                        {{ scope.row.group }}
                      </el-tag>
                      {{ scope.row.runId }}
                    </div>
                  </template>
                </el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('bioProjectId')"
                  label="BioProject ID"
                  prop="bioProjectId"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('latitude')"
                  label="Latitude"
                  prop="latitude"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('longitude')"
                  label="Longitude"
                  prop="longitude"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('hydrosphereType')"
                  label="Hydrosphere Type"
                  prop="hydrosphereType"
                  width="160"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('geolocation')"
                  label="Geolocation"
                  prop="waterBodyTypeByGeographic"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('waterBodyType')"
                  label="Water Body Type"
                  prop="waterBodyTypeByClassification"
                  width="160"
                ></el-table-column>

                <!-- Hidden columns that can be toggled -->
                <el-table-column
                  v-if="getColumnVisibility('depth')"
                  label="Depth"
                  prop="depth"
                  width="100"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('temperature')"
                  label="Temperature"
                  prop="temperature"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('salinity')"
                  label="Salinity"
                  prop="salinity"
                  width="100"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('ph')"
                  label="pH"
                  prop="ph"
                  width="80"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('criticalZone')"
                  label="Critical Zone"
                  prop="criticalZone"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('samplingSubstrate')"
                  label="Sampling Substrate"
                  prop="samplingSubstrate"
                  width="150"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('country')"
                  label="Country"
                  prop="country"
                  width="120"
                ></el-table-column>

                <el-table-column
                  v-if="getColumnVisibility('waterBodyName')"
                  label="Water Body Name"
                  prop="waterBodyName"
                  width="160"
                ></el-table-column>

                <!-- Analysis Results column (always visible) -->
                <el-table-column
                  v-if="getColumnVisibility('analysisResults')"
                  label="Analysis Results"
                  prop="analysisResults"
                  width="160"
                  align="center"
                >
                  <template #default="scope">
                    <router-link :to="`/sample/detail/${scope.row.runId}`">
                      <div class="text-primary">View</div>
                    </router-link>
                  </template>
                </el-table-column>
              </el-table>

              <el-pagination
                v-model:current-page="currentPage"
                v-model:page-size="pageSize"
                class="mb-1 mt-2 justify-center"
                :page-sizes="[100, 200, 300, 400]"
                layout="total, sizes, prev, pager, next"
                :total="dataTable.value.length"
              />
            </div>
          </div>
        </el-col>
      </el-row>
    </div>
    <BrowseCart />
  </div>
</template>

<script setup>
  import {
    computed,
    getCurrentInstance,
    nextTick,
    onMounted,
    ref,
    toRefs,
    watch,
  } from 'vue';
  import {
    DataAnalysis,
    Menu,
    Minus,
    Plus,
    Promotion,
  } from '@element-plus/icons-vue';
  import L from 'leaflet';
  import 'leaflet/dist/leaflet.css';
  import * as echarts from 'echarts';

  import BrowseCart from '@/components/ShoppingCart/BrowseCart.vue';
  import {
    createBiogeography,
    getResultMapData,
    getSpeciesName,
    getSpeciesNameList,
    getTableResult,
  } from '@/api/diversity';
  import { getWaterBodyTypeByHydrosphere } from '@/api/samples';
  import { useCartStore } from '@/store/modules/cart';
  import axios from 'axios';

  // Swiper imports
  import 'swiper/css';
  import 'swiper/css/navigation';
  import 'swiper/css/pagination';
  import { Swiper, SwiperSlide } from 'swiper/vue';
  import { Navigation, Pagination } from 'swiper/modules';

  const { proxy } = getCurrentInstance();

  const cartStore = useCartStore();

  const form = ref({
    analysisType: 'Biogeography',
    domain: 'A',
    taxonomy: 'P',
    speciesNames: [],
    sliderLongitude: [-180.0, 180.0],
    sliderLatitude: [-90, 90],
    longitudeTo: -180.0,
    longitudeFrom: 180.0,
    latitudeTo: -90,
    latitudeFrom: 90,
    waterBodyType: [],
    geolocation: '',
    type: 'From Biota',
    selectedGroup: '', // 改为单选，存储选中的group名称
    diversityGroups: [
      {
        id: 1,
        name: 'Group A',
        sliderLongitude: [-180.0, 180.0],
        sliderLatitude: [-90, 90],
        longitudeTo: -180.0,
        longitudeFrom: 180.0,
        latitudeTo: -90,
        latitudeFrom: 90,
        waterBodyType: 'marine',
        geolocation: '',
        selectedGroups: '',
      },
    ],
  });

  // biogeography 相关数据
  const biogeographyData = reactive({
    submitLoading: false,
    taskId: '',
    speciesNameList: [], // 地图下拉框选项
    summarySelect: '', // 当前选中的物种
    hasResults: false, // 是否有结果数据
    sampleStatistics: {
      detectedSamples: 0,
      selectedSamples: 0,
      min: 0,
      median: 0,
      max: 0,
    },
  });

  // 解构 biogeographyData 中的响应式属性
  const { submitLoading, taskId, speciesNameList, summarySelect, hasResults } =
    toRefs(biogeographyData);

  const speciesNameOptions = ref([]);
  // 从购物车store获取分组选项
  const groupOptions = computed(() => {
    return cartStore.cartGroups.map(group => ({
      label: `${group.name} (${group.runCount} runs)`,
      value: group.name,
    }));
  });
  const table = ref(null);
  const currentPage = ref(1);
  const pageSize = ref(10);

  // 购物车选择相关
  const selectedCartCount = ref(0);
  const hydrosphereOpt = [];
  const props = {
    expandTrigger: 'hover',
  };

  // Swiper 相关
  const swiperModules = [Navigation, Pagination];
  const chartSwiper = ref(null);

  const onChartSwiper = swiper => {
    chartSwiper.value = swiper;
  };

  const barRef = ref(null);
  let barInstance = null;

  const ldaRef = ref(null);
  let ldaInstance = null;

  const isMapFullscreen = ref(false);

  // 样本统计信息
  const sampleStatistics = ref({
    detectedSamples: 0,
    selectedSamples: 0,
    min: '',
    max: '',
    median: '',
  });

  // 当前数据层引用
  let currentDataLayer = null;

  // 地理数据
  const oceanData = ref(null);
  const lakesData = ref(null);
  const riversData = ref(null);
  const geoDataLoading = ref(false);

  // 动态加载的水体类型选项，参考genomic页面实现
  const waterBodyOpt = ref([
    {
      value: 'water_body_type_by_geographic',
      label: 'Geolocation',
      children: [],
    },
    {
      value: 'water_body_type_by_classification',
      label: 'Water Body Type',
      children: [],
    },
  ]);

  // 监听滑动条变化，同步到输入框
  watch(
    () => form.value.sliderLongitude,
    newVal => {
      form.value.longitudeTo = newVal[0];
      form.value.longitudeFrom = newVal[1];
    },
  );

  watch(
    () => form.value.sliderLatitude,
    newVal => {
      form.value.latitudeTo = newVal[0];
      form.value.latitudeFrom = newVal[1];
    },
  );

  // 监听输入框变化，同步到滑动条
  watch(
    () => [form.value.longitudeTo, form.value.longitudeFrom],
    newVal => {
      form.value.sliderLongitude = [newVal[0], newVal[1]];
    },
  );

  watch(
    () => [form.value.latitudeTo, form.value.latitudeFrom],
    newVal => {
      form.value.sliderLatitude = [newVal[0], newVal[1]];
    },
  );

  // 监听selectedGroup变化，提供用户反馈
  watch(
    () => form.value.selectedGroup,
    (newVal, oldVal) => {
      if (newVal && !oldVal) {
        // 选中了group
        console.log(`Selected group: ${newVal}`);
      } else if (!newVal && oldVal) {
        // 清除了group选择
        console.log('Cleared group selection, will use filter conditions');
      }
    },
  );

  // 监听domain和taxonomy变化，获取物种名称选项
  watch(
    () => [form.value.domain, form.value.taxonomy],
    ([domain, taxonomy]) => {
      if (domain && taxonomy) {
        form.value.speciesNames = [];
        fetchSpeciesNameOptions(domain, taxonomy);
      }
    },
    { immediate: true },
  );

  // 监听summarySelect变化，自动更新地图和表格数据
  watch(
    () => summarySelect.value,
    newVal => {
      if (newVal && taskId.value) {
        fetchMapData();
        fetchTableData();
      }
    },
  );

  // 监听hasResults变化，当显示结果时重新渲染地图
  watch(
    () => hasResults.value,
    newVal => {
      if (newVal) {
        nextTick(() => {
          setTimeout(() => {
            if (window.mapInstance) {
              window.mapInstance.invalidateSize();
            }
          }, 100);
        });
      }
    },
  );

  // 全屏切换方法
  function toggleMapFullscreen() {
    isMapFullscreen.value = !isMapFullscreen.value;
    nextTick(() => {
      setTimeout(() => {
        const mapElement = document.getElementById('diversityMap');
        if (mapElement && window.mapInstance) {
          window.mapInstance.invalidateSize();
        }
      }, 300);
    });
  }

  // 获取物种名称选项
  function fetchSpeciesNameOptions(domain, taxonomy) {
    getSpeciesName({
      domain: domain,
      level: taxonomy,
      search: '',
    })
      .then(response => {
          speciesNameOptions.value = response.data;
        }
      })
      .catch(error => {
        console.error('获取物种名称选项失败:', error);
      });
  }

  // 处理级联选择器变化
  function handleWaterBodyTypeChange(value) {
    console.log('Water body type changed:', value);
    if (value && value.length > 0) {
      const category = value[0]; // 一级选择
      // 当用户选择一级选项时，加载对应的二级选项
      loadSecondLevelOptions(category);
    }
  }

  // 加载水体类型级联选择器数据
  function loadWaterBodyTypeOptions() {
    console.log('Loading water body type options...');
    // 初始化时加载所有分类的二级选项
    loadSecondLevelOptions('water_body_type_by_geographic');
    loadSecondLevelOptions('water_body_type_by_classification');
  }

  // 加载指定分类的二级选项
  function loadSecondLevelOptions(category) {
    console.log('Loading second level options for category:', category);

    getWaterBodyTypeByHydrosphere({
      hydrosphereType: 'All',
      category: category,
    })
      .then(response => {
        console.log(`${category} response:`, response);
        if (response.data && response.data.length) {
          const options = response.data.map(item => ({
            label: item,
            value: item,
          }));

          // 找到对应的一级选项并更新其children
          const targetIndex =
            category === 'water_body_type_by_geographic' ? 0 : 1;
          waterBodyOpt.value[targetIndex].children = options;
          console.log(`Updated ${category} options:`, options);
        } else {
          console.log(`No ${category} data received`);
        }
      })
      .catch(error => {
        console.error(`Failed to load ${category} options:`, error);
      });
  }

  // 提交biogeography任务
  function submitBiogeography() {
    if (form.value.speciesNames.length === 0) {
      proxy.$modal.msgError('Please select at least one species name');
      return;
    }

    if (form.value.speciesNames.length > 10) {
      proxy.$modal.msgError('Maximum 10 species names allowed');
      return;
    }

    // 开始提交
    biogeographyData.value.submitLoading = true;

    // 显示全局遮罩
    proxy.$modal.loading('Submitting analysis request...');

    // 构建提交参数
    const params = {
      domain: form.value.domain,
      taxonomy: form.value.taxonomy,
      speciesNames: form.value.speciesNames,
    };

    // 根据是否选中group来决定提交的数据
    if (form.value.selectedGroup) {
      // 如果选中了group，提交group的runIds
      const selectedGroupData = cartStore.cartGroups.find(
        group => group.name === form.value.selectedGroup,
      );
      if (selectedGroupData) {
        params.runIds = selectedGroupData.runIds;
      } else {
        proxy.$modal.closeLoading();
        proxy.$modal.msgError('Selected group not found in cart');
        biogeographyData.value.submitLoading = false;
        return;
      }
    } else {
      // 如果没有选中group，使用筛选条件
      params.runIds = [];
      params.queryDTO = {
        latitudeStart: form.value.latitudeTo,
        latitudeEnd: form.value.latitudeFrom,
        longitudeStart: form.value.longitudeTo,
        longitudeEnd: form.value.longitudeFrom,
      };

      // 根据一级下拉框的选择决定提交字段
      if (
        Array.isArray(form.value.waterBodyType) &&
        form.value.waterBodyType.length >= 2
      ) {
        const firstLevel = form.value.waterBodyType[0];
        const secondLevel = form.value.waterBodyType[1];

        if (firstLevel === 'water_body_type_by_geographic') {
          params.queryDTO.waterBodyTypeByGeographic = secondLevel;
        } else if (firstLevel === 'water_body_type_by_classification') {
          params.queryDTO.waterBodyTypeByClassification = secondLevel;
        }
      }
    }

    createBiogeography(params)
      .then(response => {
        taskId.value = response.msg;
        // 任务提交成功后，获取物种名称列表
        fetchSpeciesNameList();
        proxy.$modal.msgSuccess('Analysis submitted successfully');
      })
      .catch(error => {
        console.error('提交任务失败:', error);
        proxy.$modal.msgError('Failed to submit analysis');
      })
      .finally(() => {
        proxy.$modal.closeLoading();
        biogeographyData.value.submitLoading = false;
      });
  }

  // 获取物种名称列表（用于地图下拉框）
  function fetchSpeciesNameList() {
    if (!taskId.value) return;

    getSpeciesNameList({ taskId: taskId.value })
      .then(response => {
        speciesNameList.value = response.data || [];

        // 默认选中第一个
        if (speciesNameList.value.length > 0) {
          summarySelect.value = biogeographyData.value.speciesNameList[0];
          // 设置有结果状态
          hasResults.value = true;
          // 获取地图数据和表格数据
          fetchMapData();
          fetchTableData();
        }
      })
      .catch(error => {
        console.error('获取物种名称列表失败:', error);
      });
  }

  // 获取地图数据
  function fetchMapData() {
    if (!taskId.value || !summarySelect.value) return;

    getResultMapData({
      taskId: taskId.value,
      speciesName: summarySelect.value,
    })
      .then(response => {
        const result = response.data;
        // 检查是否返回 null（没有分析结果）
        if (result) {
          // 正常处理数据
          const data = result || {};
          updateMapWithData(data.mapData || []);
          updateSampleStatistics(data);

        }else {
          proxy.$modal
              .confirm(`Taxonomy unknown: ${summarySelect.value}`)
          // 显示空地图和空统计信息
          updateMapWithData([]);
          updateSampleStatistics({});
        }

      })
      .catch(error => {
        console.error('获取地图数据失败:', error);
      });
  }

  // 获取表格数据
  function fetchTableData() {
    if (!taskId.value || !summarySelect.value) return;

    getTableResult({
      taskId: taskId.value,
      speciesName: summarySelect.value,
    })
      .then(response => {
        const result = response.data;
        console.log(result)

        // 检查是否返回 null（没有分析结果）
        if (result) {
          dataTable.value = result.content
        }else {
          dataTable.value = []
        }

      })
      .catch(error => {
        console.error('获取表格数据失败:', error);
      });
  }

  // 更新地图数据
  function updateMapWithData(mapData) {
    if (!window.mapInstance || !mapData || mapData.length === 0) {
      return;
    }

    // 清除之前的数据层
    if (currentDataLayer) {
      window.mapInstance.removeLayer(currentDataLayer);
    }

    // 创建canvas渲染器
    const canvasRenderer = L.canvas({ padding: 0.5 });

    // 创建图层组
    const pointsLayer = L.layerGroup();

    mapData.forEach(item => {
      if (item.latitude && item.longitude && item.number > 0) {
        // 根据value值计算圆点大小，使用提供的公式
        const value = item.size || item.number; // 使用size字段，如果没有则使用number
        const radius = Math.log10(value * 8) * 2.1;
        const finalRadius = Math.max(3, Math.min(35, radius)); // 限制最小和最大半径

        const circleMarker = L.circleMarker([item.latitude, item.longitude], {
          radius: finalRadius,
          fillColor: '#C6A5F4',
          color: '#C6A5F4',
          opacity: 1,
          weight: 0.5,
          fillOpacity: 1,
          pane: 'pointsPane',
          renderer: canvasRenderer,
        });

        // 添加弹出框显示详细信息
        const formattedLatitude = formatNumber(item.latitude);
        const formattedLongitude = formatNumber(item.longitude);
        const formattedMaxAbundance = formatNumber(value / 10000);

        circleMarker.bindPopup(`
          <div style="font-family: Arial, sans-serif; line-height: 1.4;">
            <div style="margin-bottom: 4px;">
              <strong>Sample Count:</strong> <span style="color: #E6A23C;">${item.number}</span>
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Latitude:</strong> ${formattedLatitude}°
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Longitude:</strong> ${formattedLongitude}°
            </div>
            <div style="margin-bottom: 4px;">
              <strong>Max Abundance:</strong> ${formattedMaxAbundance}
            </div>
          </div>
        `);

        pointsLayer.addLayer(circleMarker);
      }
    });

    // 添加到地图
    if (pointsLayer.getLayers().length > 0) {
      pointsLayer.addTo(window.mapInstance);
      currentDataLayer = pointsLayer;
    }
  }

  // 数字格式化函数
  function formatNumber(number) {
    const decimalPart = number.toString().split('.')[1];
    if (decimalPart && decimalPart.length > 4) {
      return number.toFixed(4);
    } else {
      return number;
    }
  }

  // 更新样本统计信息
  function updateSampleStatistics(result) {
    sampleStatistics.value.detectedSamples = result.detectedNum || 0;
    sampleStatistics.value.selectedSamples = result.selectedNum || 0;
    sampleStatistics.value.min = formatNumber(parseFloat(result.min || 0));
    sampleStatistics.value.max = formatNumber(parseFloat(result.max || 0));
    sampleStatistics.value.median = formatNumber(
      parseFloat(result.median || 0),
    );
  }

  // 异步获取地理数据的函数
  async function fetchGeoData() {
    try {
      geoDataLoading.value = true;
      // 获取基础路径
      const basePath = import.meta.env.VITE_APP_PUBLIC_PATH || '/';

      const [oceanResponse, lakesResponse, riversResponse] = await Promise.all([
        axios.get(`${basePath}/geojson/ocean.json`),
        axios.get(`${basePath}/geojson/sample_lakes.json`),
        axios.get(`${basePath}/geojson/sample_rivers.json`),
      ]);

      oceanData.value = oceanResponse.data;
      lakesData.value = lakesResponse.data;
      riversData.value = riversResponse.data;
      return true;
    } catch (error) {
      console.error('加载地理数据失败:', error);
      return false;
    } finally {
      geoDataLoading.value = false;
    }
  }

  // 添加分组
  function addGroup() {
    const newGroupId = form.value.diversityGroups.length + 1;
    const groupLetter = String.fromCharCode(64 + newGroupId); // A, B, C, etc.

    form.value.diversityGroups.push({
      id: newGroupId,
      name: `Group ${groupLetter}`,
      sliderLongitude: [-180.0, 180.0],
      sliderLatitude: [-90, 90],
      longitudeTo: -180.0,
      longitudeFrom: 180.0,
      latitudeTo: -90,
      latitudeFrom: 90,
      hydrosphereType: 'marine',
      geolocation: '',
    });
  }

  // 移除分组
  function removeGroup(index) {
    if (form.value.diversityGroups.length > 1) {
      form.value.diversityGroups.splice(index, 1);
    }
  }

  const dataTable = ref([]);

  // 显示隐藏列
  const allColumns = ref([
    { prop: 'runId', label: 'Run ID', visible: true, default: true },
    {
      prop: 'bioProjectId',
      label: 'BioProject ID',
      visible: true,
      default: true,
    },
    { prop: 'latitude', label: 'Latitude', visible: true, default: true },
    { prop: 'longitude', label: 'Longitude', visible: true, default: true },
    {
      prop: 'hydrosphereType',
      label: 'Hydrosphere Type',
      visible: true,
      default: true,
    },
    { prop: 'geolocation', label: 'Geolocation', visible: true, default: true },
    {
      prop: 'waterBodyType',
      label: 'Water Body Type',
      visible: true,
      default: true,
    },
    {
      prop: 'analysisResults',
      label: 'Analysis Results',
      visible: true,
      default: true,
    },
    { prop: 'depth', label: 'Depth', visible: false, default: false },
    {
      prop: 'temperature',
      label: 'Temperature',
      visible: false,
      default: false,
    },
    { prop: 'salinity', label: 'Salinity', visible: false, default: false },
    { prop: 'ph', label: 'pH', visible: false, default: false },
    {
      prop: 'criticalZone',
      label: 'Critical Zone',
      visible: false,
      default: false,
    },
    {
      prop: 'samplingSubstrate',
      label: 'Sampling Substrate',
      visible: false,
      default: false,
    },
    { prop: 'country', label: 'Country', visible: false, default: false },
    {
      prop: 'waterBodyName',
      label: 'Water Body Name',
      visible: false,
      default: false,
    },
  ]);

  function getColumnVisibility(prop) {
    const column = allColumns.value.find(col => col.prop === prop);
    return column ? column.visible : false;
  }

  // 获取分组标签类型
  function getGroupTagType(group) {
    const groupTypes = {
      'Group A': 'primary',
      'Group B': 'warning',
      'Group C': 'success',
      'Group D': 'info',
      'Group E': 'danger',
    };
    return groupTypes[group] || 'info';
  }

  const initMap = id => {
    // 检查所有地理数据是否已加载
    if (!oceanData.value || !lakesData.value || !riversData.value) {
      console.warn('地理数据尚未加载完成，等待加载...');
      return null;
    }

    var latlng = L.latLng(30, 0);

    var map = L.map(id, {
      center: latlng,
      zoom: 2,
      minZoom: 2,
      maxZoom: 18,
      zoomControl: false,
      attributionControl: false,
      maxBounds: [
        [-90, -180],
        [90, 180],
      ],
    });

    map.createPane('oceanPane');
    map.createPane('riverPane');
    map.createPane('pointsPane');

    map.getPane('oceanPane').style.zIndex = 300; // 海洋图层
    map.getPane('riverPane').style.zIndex = 400; // 河流图层
    map.getPane('pointsPane').style.zIndex = 500; // 圆点图层

    const canvasRenderer = L.canvas({ padding: 0.5 });

    L.geoJSON(oceanData.value, {
      onEachFeature: function (feature, layer) {
        let labelLatLng;
        // 根据特征名称选择标签位置
        if (feature.properties.name === 'North Pacific Ocean') {
          labelLatLng = L.latLng(30, -150);
        } else if (feature.properties.name === 'South Pacific Ocean') {
          labelLatLng = L.latLng(-30, -140);
        } else {
          // 默认使用中心点
          labelLatLng = layer.getBounds().getCenter();
        }

        // 创建一个标记
        var label = L.marker(labelLatLng, {
          icon: L.divIcon({
            className: 'ocean-label',
            html: feature.properties.name,
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
      },

      style: function () {
        return {
          fillColor: '#1C4F80', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // var lakeLayer = null;
    L.geoJSON(lakesData.value, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let zoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: zoom > 4 ? feature.properties.Name : '',
            }),
          );
        });
      },

      style: function () {
        return {
          fillColor: '#9ABAE7', // 设置填充颜色为蓝色
          weight: 1,
          opacity: 1, // 不透明度设置为 1
          color: 'rgba(0, 0, 0, 0)', // 边界颜色设置为透明
          fillOpacity: 1, // 填充不透明度
          pane: 'oceanPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    L.geoJSON(riversData.value, {
      onEachFeature: function (feature, layer) {
        // 创建一个标记
        var label = L.marker(layer.getBounds().getCenter(), {
          icon: L.divIcon({
            iconSize: [100, 20],
          }),
        });
        label.addTo(map); // 将标签添加到地图
        map.on('zoomend', function () {
          let riverZoom = map.getZoom();
          label.setIcon(
            L.divIcon({
              className: 'lake-label',
              html: riverZoom > 4 ? feature.properties.name : '',
            }),
          );
        });
      },
      style: function () {
        return {
          color: '#9ABAE7',
          opacity: 1,
          weight: 1,
          fillOpacity: 1,
          pane: 'riverPane',
          renderer: canvasRenderer, // 使用 Canvas 渲染
        };
      },
    }).addTo(map);

    // 保存地图实例到全局变量，用于全屏切换时调整大小
    window.mapInstance = map;
    return map;
  };

  function getSize(abundance) {
    var size = null;
    if (abundance < 0.0001 || (abundance >= 0.0001 && abundance < 0.001)) {
      size = 4;
    }
    if (abundance >= 0.001 && abundance < 0.01) {
      size = 6;
    }
    if (abundance >= 0.01 && abundance < 0.1) {
      size = 8;
    }
    if (abundance >= 0.1 && abundance < 1) {
      size = 10;
    }
    if (abundance >= 1 && abundance < 10) {
      size = 12;
    }
    if (abundance >= 10 && abundance < 100) {
      size = 14;
    }
    if (abundance >= 100) {
      size = 16;
    }
    return size;
  }

  // 堆积柱形图
  function initStackedBarChart() {
    const chartDom = document.getElementById('chart01');
    if (!chartDom) return;
    const myChart = echarts.init(chartDom);

    const mockApiData = {
      GroupA: {
        Thaumarchaeota: [
          ['SRR7648270', 'GroupA', 'Thaumarchaeota', '69.6221'],
          ['SRR7648271', 'GroupA', 'Thaumarchaeota', '76.4788'],
          ['SRR7648272', 'GroupA', 'Thaumarchaeota', '67.8934'],
          ['SRR7648273', 'GroupA', 'Thaumarchaeota', '71.2456'],
          ['SRR7648274', 'GroupA', 'Thaumarchaeota', '68.9123'],
          ['SRR7648275', 'GroupA', 'Thaumarchaeota', '84.5678'],
          ['SRR7648276', 'GroupA', 'Thaumarchaeota', '85.1234'],
          ['SRR7648277', 'GroupA', 'Thaumarchaeota', '54.7890'],
        ],
        Euryarchaeota: [
          ['SRR7648270', 'GroupA', 'Euryarchaeota', '25.1234'],
          ['SRR7648271', 'GroupA', 'Euryarchaeota', '18.2345'],
          ['SRR7648272', 'GroupA', 'Euryarchaeota', '26.7890'],
          ['SRR7648273', 'GroupA', 'Euryarchaeota', '23.4567'],
          ['SRR7648274', 'GroupA', 'Euryarchaeota', '25.8901'],
          ['SRR7648275', 'GroupA', 'Euryarchaeota', '12.3456'],
          ['SRR7648276', 'GroupA', 'Euryarchaeota', '11.7890'],
          ['SRR7648277', 'GroupA', 'Euryarchaeota', '37.8901'],
        ],
        Crenarchaeota: [
          ['SRR7648270', 'GroupA', 'Crenarchaeota', '3.1234'],
          ['SRR7648271', 'GroupA', 'Crenarchaeota', '2.2345'],
          ['SRR7648272', 'GroupA', 'Crenarchaeota', '2.7890'],
          ['SRR7648273', 'GroupA', 'Crenarchaeota', '2.8456'],
          ['SRR7648274', 'GroupA', 'Crenarchaeota', '2.3901'],
          ['SRR7648275', 'GroupA', 'Crenarchaeota', '1.5456'],
          ['SRR7648276', 'GroupA', 'Crenarchaeota', '1.8890'],
          ['SRR7648277', 'GroupA', 'Crenarchaeota', '3.6901'],
        ],
        Others: [
          ['SRR7648270', 'GroupA', 'Others', '2.1311'],
          ['SRR7648271', 'GroupA', 'Others', '3.0522'],
          ['SRR7648272', 'GroupA', 'Others', '2.5286'],
          ['SRR7648273', 'GroupA', 'Others', '2.4521'],
          ['SRR7648274', 'GroupA', 'Others', '2.8175'],
          ['SRR7648275', 'GroupA', 'Others', '1.541'],
          ['SRR7648276', 'GroupA', 'Others', '1.1986'],
          ['SRR7648277', 'GroupA', 'Others', '3.6308'],
        ],
      },
      GroupB: {
        Thaumarchaeota: [
          ['SRR7648281', 'GroupB', 'Thaumarchaeota', '70.8234'],
          ['SRR7648282', 'GroupB', 'Thaumarchaeota', '77.5678'],
          ['SRR7648283', 'GroupB', 'Thaumarchaeota', '69.9012'],
          ['SRR7648284', 'GroupB', 'Thaumarchaeota', '80.3456'],
          ['SRR7648285', 'GroupB', 'Thaumarchaeota', '51.7890'],
          ['SRR7648286', 'GroupB', 'Thaumarchaeota', '48.2345'],
          ['SRR7648287', 'GroupB', 'Thaumarchaeota', '71.6789'],
          ['SRR7648288', 'GroupB', 'Thaumarchaeota', '57.9012'],
        ],
        Euryarchaeota: [
          ['SRR7648281', 'GroupB', 'Euryarchaeota', '24.1234'],
          ['SRR7648282', 'GroupB', 'Euryarchaeota', '18.3456'],
          ['SRR7648283', 'GroupB', 'Euryarchaeota', '25.7890'],
          ['SRR7648284', 'GroupB', 'Euryarchaeota', '15.2345'],
          ['SRR7648285', 'GroupB', 'Euryarchaeota', '41.6789'],
          ['SRR7648286', 'GroupB', 'Euryarchaeota', '44.8012'],
          ['SRR7648287', 'GroupB', 'Euryarchaeota', '23.4567'],
          ['SRR7648288', 'GroupB', 'Euryarchaeota', '35.8901'],
        ],
        Crenarchaeota: [
          ['SRR7648281', 'GroupB', 'Crenarchaeota', '2.5234'],
          ['SRR7648282', 'GroupB', 'Crenarchaeota', '2.1456'],
          ['SRR7648283', 'GroupB', 'Crenarchaeota', '2.8890'],
          ['SRR7648284', 'GroupB', 'Crenarchaeota', '2.0345'],
          ['SRR7648285', 'GroupB', 'Crenarchaeota', '3.2789'],
          ['SRR7648286', 'GroupB', 'Crenarchaeota', '3.6012'],
          ['SRR7648287', 'GroupB', 'Crenarchaeota', '2.5567'],
          ['SRR7648288', 'GroupB', 'Crenarchaeota', '3.1901'],
        ],
        Others: [
          ['SRR7648281', 'GroupB', 'Others', '2.5532'],
          ['SRR7648282', 'GroupB', 'Others', '2.041'],
          ['SRR7648283', 'GroupB', 'Others', '1.422'],
          ['SRR7648284', 'GroupB', 'Others', '2.3854'],
          ['SRR7648285', 'GroupB', 'Others', '3.2532'],
          ['SRR7648286', 'GroupB', 'Others', '3.3631'],
          ['SRR7648287', 'GroupB', 'Others', '2.3077'],
          ['SRR7648288', 'GroupB', 'Others', '3.0186'],
        ],
      },
    };

    // 提取所有物种类别和颜色配置
    const allSpecies = new Set();
    Object.keys(mockApiData).forEach(groupName => {
      Object.keys(mockApiData[groupName]).forEach(species => {
        allSpecies.add(species);
      });
    });

    const categories = Array.from(allSpecies);
    const colors = {
      Thaumarchaeota: '#4877C3', // 基准鲜艳橙色
      Euryarchaeota: '#93C97B', // 鲜艳蓝色 (与橙色互补)
      Crenarchaeota: '#FFC363', // 鲜艳绿色
      Candidatus: '#F26467', // 鲜艳紫色
      Nanoarchaeota: '#73C3CC', // 鲜艳洋红色
      Others: '#9E9E9E', // 明亮灰色
    };

    // 处理数据，按Group和Sample组织
    function processApiData(apiData) {
      const processedData = {};

      Object.keys(apiData).forEach(groupName => {
        const groupData = apiData[groupName];
        const sampleMap = new Map();

        // 收集该Group的所有样本ID
        Object.keys(groupData).forEach(species => {
          groupData[species].forEach(item => {
            const [sampleId, group, speciesName, abundance] = item;
            if (!sampleMap.has(sampleId)) {
              sampleMap.set(sampleId, {});
            }
            sampleMap.get(sampleId)[speciesName] = parseFloat(abundance);
          });
        });

        processedData[groupName] = {
          samples: Array.from(sampleMap.keys()).sort(),
          data: sampleMap,
        };
      });

      return processedData;
    }

    const processedData = processApiData(mockApiData);
    const groupALabels = processedData.GroupA.samples;
    const groupBLabels = processedData.GroupB.samples;

    const groupASeries = categories.map(species => ({
      name: species,
      type: 'bar',
      stack: 'groupA',
      xAxisIndex: 0,
      yAxisIndex: 0,
      data: groupALabels.map(sampleId => {
        const sampleData = processedData.GroupA.data.get(sampleId);
        return sampleData && sampleData[species] ? sampleData[species] : 0;
      }),
      itemStyle: {
        color: colors[species] || '#7f7f7f',
      },
    }));

    const groupBSeries = categories.map(species => ({
      name: `${species}_B`,
      type: 'bar',
      stack: 'groupB',
      xAxisIndex: 1,
      yAxisIndex: 1,
      data: groupBLabels.map(sampleId => {
        const sampleData = processedData.GroupB.data.get(sampleId);
        return sampleData && sampleData[species] ? sampleData[species] : 0;
      }),
      itemStyle: {
        color: colors[species] || '#7f7f7f',
      },
      showInLegend: false,
    }));

    const allSeries = [...groupASeries, ...groupBSeries];

    const option = {
      title: {
        text: '',
        left: 'center',
      },
      tooltip: {
        show: true,
      },
      legend: {
        data: categories,
        bottom: 0,
        itemWidth: 12,
        itemHeight: 12,
      },
      grid: [
        {
          left: '4%',
          right: '51%',
          bottom: '20%',
          top: '10%',
        },
        {
          left: '51%',
          right: '4%',
          bottom: '20%',
          top: '10%',
        },
      ],
      xAxis: [
        {
          type: 'category',
          data: groupALabels,
          gridIndex: 0,
          axisLabel: {
            rotate: 45,
            fontSize: 10,
          },
        },
        {
          type: 'category',
          data: groupBLabels,
          gridIndex: 1,
          axisLabel: {
            rotate: 45,
            fontSize: 10,
          },
        },
      ],
      yAxis: [
        {
          type: 'value',
          name: 'Taxonomic Composition',
          max: 100,
          gridIndex: 0,
          axisLabel: {
            formatter: '{value}',
          },
          nameLocation: 'center',
          nameRotate: 90,
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
        {
          type: 'value',
          name: 'Taxonomic Composition',
          max: 100,
          show: false,
          gridIndex: 1,
          axisLabel: {
            formatter: '{value}',
          },
          nameLocation: 'center',
          nameRotate: 90,
          nameGap: 30,
          nameTextStyle: {
            fontSize: 14,
            fontWeight: 'normal',
          },
        },
      ],
      series: allSeries,
      graphic: [
        {
          type: 'text',
          left: '23%',
          top: '0%',
          style: {
            text: 'GroupA',
            fontSize: 16,
            fontWeight: 'bold',
            fill: '#1f77b4',
          },
        },
        {
          type: 'text',
          left: '73%',
          top: '0%',
          style: {
            text: 'GroupB',
            fontSize: 16,
            fontWeight: 'bold',
            fill: '#ff7f0e',
          },
        },
      ],
    };

    myChart.setOption(option);

    // Handle window resize
    window.addEventListener('resize', () => {
      myChart.resize();
    });
  }

  // 初始化LDA柱形图 - 单grid完整X轴实现
  function initLDAChart() {
    const chartDom = document.getElementById('ldaChart');
    if (!chartDom) {
      console.error('LDA Chart container not found!');
      return;
    }

    const myChart = echarts.init(chartDom);

    // 按照图片顺序排列所有数据
    const allGroups = [
      { name: 'Candidatus', value: -0.8, group: 'Control' },
      { name: 'Roseburia', value: -1.2, group: 'Control' },
      { name: 'Verrucomicrobiaceae', value: -1.6, group: 'Control' },
      { name: 'Verrucomicrobia', value: -2.0, group: 'Control' },
      { name: 'Akkermansia', value: -2.4, group: 'Control' },
      { name: 'Verrucomicrobiales', value: -2.8, group: 'Control' },
      { name: 'Verrucomicrobiae', value: -3.2, group: 'Control' },
      { name: 'Verrucomicrobiae2', value: -5, group: 'Control' },

      { name: 'Bacteroidales', value: 4.8, group: 'Treatment' },
      { name: 'Bacteroidetes', value: 4.6, group: 'Treatment' },
      { name: 'Bacteroidia', value: 4.4, group: 'Treatment' },
      { name: 'Parabacteroides', value: 4.2, group: 'Treatment' },
      { name: 'Parasutterella', value: 4.0, group: 'Treatment' },
      { name: 'Sutterellaceae', value: 3.8, group: 'Treatment' },
      { name: 'Betaproteobacteria', value: 3.6, group: 'Treatment' },
    ];

    const option = {
      tooltip: {},
      grid: {
        left: '-12%',
        right: '10%',
        bottom: '15%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        textStyle: {
          fontSize: 14,
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
          },
        },
      },
      // 单一Y轴，显示所有物种
      yAxis: {
        type: 'category',
        data: allGroups.map(item => item.name),
        show: false,
      },
      // 单一系列，根据数值正负显示不同颜色
      series: [
        {
          type: 'bar',
          data: allGroups.map(item => ({
            value: item.value,
            label: {
              show: true,
              position: item.group === 'Treatment' ? 'left' : 'right',
              formatter: item.name,
            },
            itemStyle: {
              color: item.group === 'Treatment' ? '#4877C3' : ' #FFBF6C',
            },
          })),
          barWidth: 20,
          itemStyle: {
            borderRadius: 0,
          },
        },
      ],
      // 添加底部X轴标签
      graphic: [
        {
          type: 'text',
          left: 'center',
          bottom: '5%',
          style: {
            text: 'LDA SCORE (log 10)',
            fontSize: 12,
            fill: '#333',
            textAlign: 'center',
          },
        },
      ],
    };

    myChart.setOption(option);

    // 处理窗口大小变化
    window.addEventListener('resize', () => {
      myChart.resize();
    });

    return myChart;
  }

  function handleAnalysisTypeChange() {
    nextTick(() => {
      if (form.value.analysisType === 'Species Diversity') {
        barInstance = echarts.init(barRef.value);
        ldaInstance = echarts.init(ldaRef.value);
        barInstance?.resize();
        ldaInstance?.resize();
      }
    });
  }

  // 处理购物车选择
  const handleCartSelection = data => {
    selectedCartCount.value = data.count;
  };

  onMounted(async () => {
    await nextTick();

    // 加载水体类型选项
    loadWaterBodyTypeOptions();

    // 先获取地理数据，然后初始化地图
    const success = await fetchGeoData();
    if (success) {
      initMap('diversityMap');
    } else {
      console.error('地理数据加载失败，无法初始化地图');
    }

    // 根据初始选择的分析类型初始化相应视图
    initStackedBarChart();
    initLDAChart();
  });
</script>

<style lang="scss" scoped>
  .container-fluid {
    max-width: 1640px !important;
  }

  .submit-page {
    padding: 120px 0 45px 0;
  }

  :deep(.el-slider__bar),
  .filter-search {
    background-color: #1e7cb2;
  }

  :deep(.el-tabs__item) {
    font-size: 16px;
    font-weight: 600;

    &.is-active {
      color: #0080b0;
    }
  }

  :deep(.el-slider__button) {
    width: 12px;
    height: 12px;
  }

  h3 {
    display: flex;
    align-items: center;
    color: #1e7cb2;
  }

  .filter-svg {
    width: 18px;
    height: 18px;
  }

  :deep(.el-popper.is-dark) {
    width: 300px;
  }

  .svg {
    width: 14px;
    height: 14px;
    margin-right: 0.3rem;
  }

  .filter {
    width: 24px;
    height: 26px;
    margin-right: 0.5rem;
  }

  :deep(.el-form-item__label) {
    font-weight: 600;
  }

  :deep(.leaflet-marker-icon.ocean-label) {
    color: #ffffff;
    font-size: 15px;
    width: 200px !important;
    z-index: 200;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.el-upload-dragger) {
    padding: 10px 0;
  }

  :deep(.el-upload-dragger .el-icon--upload) {
    color: #2668b4;
    font-size: 45px;
    margin-bottom: 0;
  }

  :deep(.el-upload-list) {
    margin: 0 !important;
  }

  :deep(.el-button--small.is-round) {
    width: 24px;
    height: 24px;
    font-size: 14px;
    padding: 0;
  }

  .legend {
    //position: absolute;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-top: 15px;
  }

  .cricle {
    background-color: #c6a5f4;
    border-radius: 50%;
  }

  .legend-item {
    display: flex;
    align-items: center;
    justify-content: space-between;

    & > div {
      margin-right: 18px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
  }

  h4 {
    text-align: center;
    font-size: 1.1rem;
    margin-right: 20px;
  }

  .lake-label {
    color: #2668b4;
    font-family: initial;
  }

  :deep(.lake-label) {
    color: #2668b4;
    font-family: initial;
    white-space: nowrap;
  }

  :deep(.leaflet-div-icon) {
    background: transparent;
    border: none;
  }

  /* New styles for the updated form */
  .species-section {
    padding: 0 0 0 16px;
  }

  .species-warning {
    color: #f56c6c;
    font-size: 12px;
    margin-top: 4px;
    line-height: 1.4;
  }

  .data-filter-section {
    padding: 0 4px 0 16px;
    flex: 1;
  }

  .diversity-group {
    border: 1px solid #dcdfe6;
    border-radius: 4px;
    padding: 16px;
    background-color: #ffffff;
    margin-bottom: 16px;

    &:last-child {
      margin-bottom: 0;
    }
  }

  .group-header {
    border-bottom: 1px solid #e4e7ed;
    padding-bottom: 8px;
    margin-bottom: 16px;
  }

  .group-title {
    color: #1e7cb2;
    font-size: 16px;
    font-weight: 600;
    margin: 0;
  }

  .group-actions {
    display: flex;
  }

  .group-content {
    .el-form-item {
      margin-bottom: 12px;
    }
  }

  .upload-section {
    flex: 1;

    .upload-demo {
      margin-top: 8px;
    }
  }

  .mb-2 {
    margin-bottom: 12px !important;
  }

  .mb-3 {
    margin-bottom: 20px !important;
  }

  :deep(.el-radio-group) {
    display: flex;
    flex-wrap: wrap;
    gap: 12px;
  }

  :deep(.el-radio) {
    margin-right: 0;
  }

  /* Section label styling */
  .section-label {
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600;
    color: #1e7cb2;
    padding-bottom: 4px;

    .el-icon {
      margin-right: 0.2rem;
    }
  }

  /* Custom radio button styling */
  .custom-radio-buttons {
    display: flex;
    flex-wrap: wrap;
    gap: 8px;

    :deep(.el-radio-button) {
      margin-right: 0;
    }

    :deep(.el-radio-button__inner) {
      background-color: #f5f5f5;
      border: 1px solid #dcdfe6;
      color: #606266;
      padding: 4px 10px !important;
      border-radius: 4px;
      transition: all 0.3s ease;

      &:hover {
        background-color: #3498db;
        border-color: #3498db;
        color: #ffffff;
      }
    }

    :deep(.el-radio-button__original-radio:checked + .el-radio-button__inner) {
      background-color: #3498db;
      border-color: #3498db;
      color: #ffffff;
      box-shadow: none;
    }

    :deep(.el-radio-button:first-child .el-radio-button__inner) {
      border-radius: 4px;
    }

    :deep(.el-radio-button:last-child .el-radio-button__inner) {
      border-radius: 4px;
    }
  }

  /* Group title input styling */
  .group-title-input {
    :deep(.el-input__wrapper) {
      background-color: transparent;
      border: none;
      box-shadow: none;
      padding: 0;
    }

    :deep(.el-input__inner) {
      color: #1e7cb2;
      font-size: 16px;
      font-weight: 600;
      text-align: left;
    }
  }

  /* Metadata popover styling */
  :deep(.metadata-popover) {
    .metadata-selector {
      .metadata-title {
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
        text-align: center;
      }

      .column-checkboxes {
        max-height: 300px;
        overflow-y: auto;
        margin-bottom: 16px;

        .column-checkbox {
          display: block;
          margin-bottom: 8px;

          :deep(.el-checkbox__label) {
            font-size: 14px;
            color: #606266;
          }
        }
      }

      .metadata-actions {
        text-align: center;
        border-top: 1px solid #e4e7ed;
        padding-top: 12px;
      }
    }
  }

  /* 购物车水平布局样式 */
  .cart-form-item {
    display: flex;
    align-items: center;
    gap: 12px;

    .cart-label {
      font-size: 14px;
      font-weight: 700;
      color: #606266;
      white-space: nowrap;
      min-width: fit-content;
    }

    .cart-section {
      flex: 1;
    }
  }

  /* 选中数据信息样式 */
  .selected-data-info {
    margin-top: 8px;
    padding-left: 0;

    .el-tag {
      font-size: 12px;
      padding: 4px 8px;
    }
  }

  /* 分组标签样式 */
  .group-tag {
    font-weight: 600;
    border-radius: 4px;
    font-size: 12px;
    padding: 2px 8px;
  }

  /* 轮播图样式 */
  .chart-swiper-container {
    width: 100%;
    position: relative;

    .chart-swiper {
      width: 100%;
      height: 400px;
      border-radius: 8px;
      overflow: hidden;

      .swiper-slide-content {
        width: 100%;
        height: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .chart-image {
          width: 100%;
          height: 100%;
          object-fit: contain;
          border-radius: 4px;
        }
      }

      // 导航按钮样式
      :deep(.swiper-button-next),
      :deep(.swiper-button-prev) {
        color: #3498db;
        background: rgba(255, 255, 255, 0.9);
        width: 40px;
        height: 40px;
        border-radius: 50%;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        transition: all 0.3s ease;

        &:hover {
          background: rgba(255, 255, 255, 1);
          transform: scale(1.1);
        }

        &::after {
          font-size: 16px;
          font-weight: 600;
        }
      }

      // 分页器样式
      :deep(.swiper-pagination) {
        bottom: 15px;

        .swiper-pagination-bullet {
          background: rgba(255, 255, 255, 0.7);
          opacity: 1;
          width: 10px;
          height: 10px;
          margin: 0 4px;
          transition: all 0.3s ease;

          &.swiper-pagination-bullet-active {
            background: #3498db;
            transform: scale(1.2);
          }
        }
      }
    }
  }

  /* map */
  .chart-card {
    position: absolute;
    right: 10px;
    bottom: 10px;
    color: #272728;
    background: rgba(255, 255, 255, 0.9);
    border-left: 1px solid #007ed31a;
    width: auto;
    min-width: 280px;
    padding: 12px;
    border-radius: 8px;
    z-index: 999;
    font-size: 14px;

    // 样本信息样式
    .sample-info {
      margin-bottom: 4px;

      .sample-count {
        color: #0080b0;
        font-weight: 600;
        margin-left: 4px;
      }
    }

    // 图例容器样式
    .legend-container {
      display: flex;
      align-items: flex-start;
      justify-content: center;
      gap: 8px;
      margin-top: 8px;
      padding-top: 8px;
      border-top: 1px dashed #999;
    }

    // 图例项样式
    .legend-item {
      display: flex;
      flex-direction: column;
      align-items: center;
      gap: 4px;

      .size-label {
        text-align: center;
        font-weight: 500;
        white-space: nowrap;
        line-height: 1;
      }

      .circle {
        background: #c3aaf1;
        border-radius: 50%;
      }
    }

    h4 {
      font-size: 14px;
      font-weight: 600;
      color: #333;
      margin: 0;
    }
  }

  .fullscreen-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;
  }

  .no-results-container {
    display: flex;
    align-items: center;
    justify-content: center;
    height: 560px;
    background-color: #fffff5;
    border: 1px solid #e0e0e0;
    border-radius: 4px;
  }

  .no-results-message {
    text-align: center;
    color: #666;
    font-size: 16px;
  }
</style>
